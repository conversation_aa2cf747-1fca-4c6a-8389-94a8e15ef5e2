"""
精准黑边矩形框激光跟踪系统
专门识别黑边矩形框，降低移动速度便于调试
"""

import time, os, gc, sys
from media.sensor import *
from media.display import *
from media.media import *
import image
import math
from machine import PWM, FPIOA, Pin

# 导入K230中断处理包装器
try:
    from k230_interrupt_wrapper import safe_run_with_interrupt_handling
    INTERRUPT_WRAPPER_AVAILABLE = True
except:
    INTERRUPT_WRAPPER_AVAILABLE = False
    print("⚠️ 中断包装器不可用，使用标准模式")

# ========== 高性能配置 ==========
DISPLAY_WIDTH = ALIGN_UP(800, 16)
DISPLAY_HEIGHT = 480
CAMERA_WIDTH = ALIGN_UP(640, 16)
CAMERA_HEIGHT = 480
DETECT_WIDTH = ALIGN_UP(320, 16)
DETECT_HEIGHT = 240

# ========== 🔧 用户可调节初始位置参数 ==========
# 💡 使用说明：
#   - 水平角度：10度=最左，135度=中心，260度=最右 (270度舵机)
#   - 垂直角度：30度=最下，90度=中心，150度=最上 (180度舵机)
#   - 修改下面的参数来调整初始位置

INITIAL_PAN_ANGLE = 118          # 🔄 水平初始角度 (10-260度) - 修改这里调整左右位置
INITIAL_TILT_ANGLE = 105          # 📐 垂直初始角度 (30-150度) - 修改这里调整上下位置

# 舵机配置
HORIZONTAL_GPIO = 42
VERTICAL_GPIO = 52
PWM_FREQ = 50
SERVO_CENTER = INITIAL_PAN_ANGLE  # 使用用户设定的初始水平角度
SERVO_CENTER_TILT = INITIAL_TILT_ANGLE  # 使用用户设定的初始垂直角度

# 270度舵机配置
IS_270_DEGREE_SERVO = True       # 🔧 水平舵机是270度舵机

# 两线激光灯配置
LASER_POSITIVE_GPIO = 3      # 激光灯正极控制引脚 (庐山派引脚8)
LASER_NEGATIVE_GPIO = 5      # 激光灯负极控制引脚 (庐山派引脚10)

# ========== 黑边矩形检测参数 ==========
# 专门针对黑边矩形优化
RECT_THRESHOLD = 9000            # 提高阈值，检测清晰边缘
MIN_AREA = 800                   # 最小面积
MAX_AREA = 25000                 # 最大面积
MIN_ASPECT_RATIO = 0.3           # 最小宽高比
MAX_ASPECT_RATIO = 3.0           # 最大宽高比
MIN_RECTANGULARITY = 0.7         # 最小矩形度
MIN_EDGE_STRENGTH = 200          # 最小边缘强度
CORNER_TOLERANCE = 15            # 角点容差4
# 黑边特征检测
BLACK_EDGE_THRESHOLD = 240        # 黑边阈值 (0-255)
EDGE_WIDTH_MIN = 3               # 最小边缘宽度
EDGE_WIDTH_MAX = 15              # 最大边缘宽度

# ========== 调试友好的移动参数 ==========
# 降低速度便于调试
HORIZONTAL_SENSITIVITY = 35.0    # 降低灵敏度
VERTICAL_SENSITIVITY = 20.0      # 降低灵敏度
HORIZONTAL_DIRECTION = -1         # 水平方向 (调试时可改为-1)
VERTICAL_DIRECTION = 1           # 纵向方向 (调试时可改为-1)

# ========== 水平搜索参数 ==========
SEARCH_ENABLED = True            # 启用搜索功能
SEARCH_MODE = "HORIZONTAL_ONLY"  # 仅水平搜索模式

# 水平搜索参数
SEARCH_SPEED = 3.0               # 搜索速度 (度/秒)
SEARCH_STEP_SIZE = 2             # 每次移动步长 (度)
SEARCH_DELAY = 0.05              # 搜索延迟 (秒)

# 🔄 270度舵机水平搜索范围 (扩大到270度覆盖)
SEARCH_PAN_MIN = 10              # 水平最小角度 (10度) - 270度舵机安全最小值
SEARCH_PAN_MAX = 260             # 水平最大角度 (260度) - 270度舵机安全最大值
SEARCH_PAN_CENTER = INITIAL_PAN_ANGLE  # 水平中心角度 - 使用用户设定的初始角度

# 垂直轴固定位置
SEARCH_TILT_FIXED = INITIAL_TILT_ANGLE  # 垂直轴固定角度 - 使用用户设定的初始角度

# 平稳移动参数
MAX_MOVE_STEP = 10.5              # 降低移动步长
MOVE_DELAY_MS = 0.000001               # 增加延迟
SMOOTHING_FACTOR = 1.1           # 降低平滑因子
LOCK_TOLERANCE = 12              # 锁定容差
DEAD_ZONE = 2                    # 死区

# 稳定性参数
STABILITY_FRAMES = 5             # 稳定帧数
DETECTION_CONFIDENCE_MIN = 2     # 最小检测置信度

# ==========================================

class TwoWireLaserController:
    """两线激光灯控制器"""

    def __init__(self):
        self.laser_positive_pin = None
        self.laser_negative_pin = None
        self.laser_enabled = False
        self.fpioa = None

        try:
            print("初始化两线激光灯控制器...")
            print(f"正极控制GPIO: {LASER_POSITIVE_GPIO} (庐山派引脚8)")
            print(f"负极控制GPIO: {LASER_NEGATIVE_GPIO} (庐山派引脚10)")

            # 初始化FPIOA和GPIO
            self.fpioa = FPIOA()
            self.fpioa.set_function(LASER_POSITIVE_GPIO, FPIOA.GPIO3)
            self.fpioa.set_function(LASER_NEGATIVE_GPIO, FPIOA.GPIO5)

            # 初始化激光灯控制引脚
            self.laser_positive_pin = Pin(LASER_POSITIVE_GPIO, Pin.OUT, pull=Pin.PULL_NONE, drive=7)
            self.laser_negative_pin = Pin(LASER_NEGATIVE_GPIO, Pin.OUT, pull=Pin.PULL_NONE, drive=7)

            # 初始状态：关闭激光灯
            self.turn_off_laser()

            print("✅ 两线激光灯控制器初始化成功")

        except Exception as e:
            print("❌ 两线激光灯初始化失败: " + str(e))

    def turn_on_laser(self):
        """开启激光灯"""
        try:
            if self.laser_positive_pin is not None and self.laser_negative_pin is not None:
                # 两线控制: 正极高电平，负极低电平
                self.laser_positive_pin.value(1)  # 正极高电平
                self.laser_negative_pin.value(0)  # 负极低电平

                self.laser_enabled = True
                print("🔴 激光灯已开启")
                return True
        except Exception as e:
            print("激光灯开启错误: " + str(e))
        return False

    def turn_off_laser(self):
        """关闭激光灯"""
        try:
            if self.laser_positive_pin is not None and self.laser_negative_pin is not None:
                # 关闭激光: 两个引脚都设为低电平
                self.laser_positive_pin.value(0)  # 正极低电平
                self.laser_negative_pin.value(0)  # 负极低电平

                self.laser_enabled = False
                print("⚫ 激光灯已关闭")
                return True
        except Exception as e:
            print("激光灯关闭错误: " + str(e))
        return False

    def is_laser_on(self):
        """检查激光灯状态"""
        return self.laser_enabled

    def get_gpio_status(self):
        """获取GPIO引脚状态"""
        try:
            if self.laser_positive_pin is not None and self.laser_negative_pin is not None:
                positive_value = self.laser_positive_pin.value()
                negative_value = self.laser_negative_pin.value()
                return positive_value, negative_value
            return "未初始化", "未初始化"
        except:
            return "读取失败", "读取失败"

    def cleanup(self):
        """清理激光灯资源"""
        try:
            self.turn_off_laser()
            print("✅ 激光灯资源已清理")
        except:
            pass

class PreciseBlackRectDetector:
    """精准黑边矩形检测器"""

    def __init__(self):
        self.detection_history = []
        self.confidence = 0
        self.last_valid_rect = None

        print("精准黑边矩形检测器初始化")
        print(f"黑边阈值: {BLACK_EDGE_THRESHOLD}")
        print(f"边缘强度: {MIN_EDGE_STRENGTH}")
        print(f"矩形度要求: {MIN_RECTANGULARITY}")

    def is_black_edge_rectangle(self, rect, img):
        """判断是否为黑边矩形"""
        try:
            rect_info = rect.rect()
            x, y, w, h = rect_info
            area = w * h

            # 1. 基本尺寸检查
            if area < MIN_AREA or area > MAX_AREA:
                return False, f"面积{area}不符合"

            # 2. 宽高比检查
            aspect_ratio = w / h if h > 0 else 0
            if aspect_ratio < MIN_ASPECT_RATIO or aspect_ratio > MAX_ASPECT_RATIO:
                return False, f"宽高比{aspect_ratio:.2f}不符合"

            # 3. 矩形度检查
            corners = rect.corners()
            if len(corners) >= 4:
                rectangularity = self.calculate_rectangularity(corners, w, h)
                if rectangularity < MIN_RECTANGULARITY:
                    return False, f"矩形度{rectangularity:.2f}不够"

            # 4. 边缘强度检查
            if hasattr(rect, 'magnitude'):
                edge_strength = rect.magnitude()
                if edge_strength < MIN_EDGE_STRENGTH:
                    return False, f"边缘强度{edge_strength}太弱"

            # 5. 黑边特征检查
            if not self.check_black_edges(img, x, y, w, h):
                return False, "黑边特征不符合"

            # 6. 角点质量检查
            if not self.check_corner_quality(corners):
                return False, "角点质量不符合"

            return True, "精准黑边矩形"

        except Exception as e:
            return False, f"检查异常: {str(e)}"

    def check_black_edges(self, img, x, y, w, h):
        """检查黑边特征"""
        try:
            # 采样边缘像素
            edge_samples = []

            # 上边缘
            for i in range(max(0, x), min(img.width(), x + w), max(1, w // 10)):
                if y > 0:
                    pixel = img.get_pixel(i, y)
                    gray = (pixel[0] + pixel[1] + pixel[2]) // 3
                    edge_samples.append(gray)

            # 下边缘
            for i in range(max(0, x), min(img.width(), x + w), max(1, w // 10)):
                if y + h < img.height():
                    pixel = img.get_pixel(i, y + h - 1)
                    gray = (pixel[0] + pixel[1] + pixel[2]) // 3
                    edge_samples.append(gray)

            # 左边缘
            for i in range(max(0, y), min(img.height(), y + h), max(1, h // 10)):
                if x > 0:
                    pixel = img.get_pixel(x, i)
                    gray = (pixel[0] + pixel[1] + pixel[2]) // 3
                    edge_samples.append(gray)

            # 右边缘
            for i in range(max(0, y), min(img.height(), y + h), max(1, h // 10)):
                if x + w < img.width():
                    pixel = img.get_pixel(x + w - 1, i)
                    gray = (pixel[0] + pixel[1] + pixel[2]) // 3
                    edge_samples.append(gray)

            # 检查是否有足够的黑边像素
            if not edge_samples:
                return False

            black_pixels = sum(1 for gray in edge_samples if gray < BLACK_EDGE_THRESHOLD)
            black_ratio = black_pixels / len(edge_samples)

            # 至少50%的边缘像素应该是黑色的
            return black_ratio > 0.5

        except:
            return False

    def calculate_rectangularity(self, corners, w, h):
        """计算矩形度"""
        if len(corners) < 4:
            return 0

        try:
            # 计算角点形成的四边形面积
            polygon_area = self.polygon_area(corners)
            rect_area = w * h

            # 矩形度 = 多边形面积 / 外接矩形面积
            rectangularity = polygon_area / rect_area if rect_area > 0 else 0
            return min(rectangularity, 1.0)
        except:
            return 0

    def polygon_area(self, corners):
        """计算多边形面积"""
        if len(corners) < 3:
            return 0

        area = 0
        n = len(corners)
        for i in range(n):
            j = (i + 1) % n
            area += corners[i][0] * corners[j][1]
            area -= corners[j][0] * corners[i][1]
        return abs(area) / 2

    def check_corner_quality(self, corners):
        """检查角点质量"""
        if len(corners) < 4:
            return False

        try:
            # 检查是否接近90度角
            angles = []
            n = len(corners)

            for i in range(n):
                p1 = corners[i]
                p2 = corners[(i + 1) % n]
                p3 = corners[(i + 2) % n]

                # 计算角度
                angle = self.calculate_angle(p1, p2, p3)
                angles.append(angle)

            # 检查角度是否接近90度
            right_angle_count = 0
            for angle in angles:
                if abs(angle - 90) < CORNER_TOLERANCE:
                    right_angle_count += 1

            # 至少要有3个接近直角的角
            return right_angle_count >= 3
        except:
            return False

    def calculate_angle(self, p1, p2, p3):
        """计算三点形成的角度"""
        try:
            # 向量
            v1 = (p1[0] - p2[0], p1[1] - p2[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # 点积和模长
            dot_product = v1[0] * v2[0] + v1[1] * v2[1]
            mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
            mag2 = math.sqrt(v2[0]**2 + v2[1]**2)

            if mag1 == 0 or mag2 == 0:
                return 0

            # 角度（弧度转角度）
            cos_angle = dot_product / (mag1 * mag2)
            cos_angle = max(-1, min(1, cos_angle))  # 限制范围
            angle = math.acos(cos_angle) * 180 / math.pi

            return angle
        except:
            return 0

    def detect_black_rectangles(self, img):
        """检测黑边矩形"""
        try:
            if img is None:
                return []

            # 基础检测
            raw_rectangles = img.find_rects(threshold=RECT_THRESHOLD)

            # 精准过滤黑边矩形
            black_rectangles = []
            for rect in raw_rectangles:
                is_black_rect, reason = self.is_black_edge_rectangle(rect, img)
                if is_black_rect:
                    black_rectangles.append(rect)
                    print(f"检测到黑边矩形: {reason}")

            # 按面积排序，选择最大的
            if black_rectangles:
                black_rectangles.sort(key=lambda r: r.rect()[2] * r.rect()[3], reverse=True)
                self.last_valid_rect = black_rectangles[0]
                self.confidence = min(self.confidence + 1, 10)
                return [black_rectangles[0]]
            else:
                self.confidence = max(self.confidence - 1, 0)
                return []

        except Exception as e:
            print(f"检测错误: {e}")
            return []

class DebugFriendlyServoController:
    """调试友好的舵机控制器 - 支持270度舵机"""

    def __init__(self):
        self.current_pan = INITIAL_PAN_ANGLE
        self.current_tilt = INITIAL_TILT_ANGLE
        self.target_pan = INITIAL_PAN_ANGLE
        self.target_tilt = INITIAL_TILT_ANGLE

        # 移动历史
        self.pan_history = [INITIAL_PAN_ANGLE] * 3
        self.tilt_history = [INITIAL_TILT_ANGLE] * 3

        # PWM对象
        self.fpioa = None
        self.pan_servo = None
        self.tilt_servo = None

        try:
            print("初始化调试友好舵机控制器 (270度舵机支持)...")
            print(f"🔧 初始位置: 水平{INITIAL_PAN_ANGLE}°, 垂直{INITIAL_TILT_ANGLE}°")
            print(f"🔧 水平舵机: {'270度舵机' if IS_270_DEGREE_SERVO else '180度舵机'}")
            print(f"移动参数: 步长{MAX_MOVE_STEP}°, 延迟{MOVE_DELAY_MS}ms, 平滑{SMOOTHING_FACTOR}")

            self.fpioa = FPIOA()
            self.fpioa.set_function(HORIZONTAL_GPIO, self.fpioa.PWM0)
            self.fpioa.set_function(VERTICAL_GPIO, self.fpioa.PWM4)

            self.pan_servo = PWM(0, PWM_FREQ, 7.5, enable=False)
            self.tilt_servo = PWM(4, PWM_FREQ, 7.5, enable=False)

            self.move_to_initial_position()
            print("✅ 调试友好舵机控制器初始化成功")

        except Exception as e:
            print("❌ 舵机初始化失败: " + str(e))

    def angle_to_duty_270(self, angle):
        """角度转占空比 (270度舵机专用)"""
        # 270度舵机：0-270度映射到2.5%-12.5%占空比
        return 2.5 + (angle / 270.0) * 10.0

    def angle_to_duty_180(self, angle):
        """角度转占空比 (180度舵机专用)"""
        # 180度舵机：0-180度映射到2.5%-12.5%占空比
        return 2.5 + (angle / 180.0) * 10.0

    def set_servo_smooth(self, servo, angle, is_pan=True):
        """平滑设置舵机角度 - 支持270度舵机"""
        try:
            if servo is None:
                return

            # 根据舵机类型设置角度限制和占空比
            if is_pan and IS_270_DEGREE_SERVO:
                # 水平轴：270度舵机安全范围
                angle = max(10, min(260, angle))
                duty = self.angle_to_duty_270(angle)
            else:
                # 垂直轴或180度舵机：标准范围
                angle = max(30, min(150, angle))
                duty = self.angle_to_duty_180(angle)

            servo.duty(duty)
            servo.enable(True)

            if is_pan:
                self.current_pan = angle
            else:
                self.current_tilt = angle

            # 调试延迟
            time.sleep_ms(MOVE_DELAY_MS)

        except Exception as e:
            servo_name = "水平" if is_pan else "纵向"
            print(servo_name + "舵机错误: " + str(e))

    def move_to_initial_position(self):
        """移动到用户设定的初始位置"""
        try:
            print(f"🔧 移动到初始位置: 水平{INITIAL_PAN_ANGLE}°, 垂直{INITIAL_TILT_ANGLE}°")
            self.set_servo_smooth(self.pan_servo, INITIAL_PAN_ANGLE, True)
            self.set_servo_smooth(self.tilt_servo, INITIAL_TILT_ANGLE, False)
            self.target_pan = INITIAL_PAN_ANGLE
            self.target_tilt = INITIAL_TILT_ANGLE
            print("✅ 舵机已到达初始位置")
        except Exception as e:
            print("移动到初始位置错误: " + str(e))

    def move_to_center(self):
        """移动到中心位置 (兼容性方法)"""
        self.move_to_initial_position()

    def debug_move_to_target(self, target_x, target_y):
        """调试友好的移动到目标位置"""
        try:
            # 计算偏移 (基于检测分辨率)
            center_x = DETECT_WIDTH // 2
            center_y = DETECT_HEIGHT // 2

            offset_x = target_x - center_x
            offset_y = target_y - center_y

            # 死区检查
            distance = math.sqrt(offset_x**2 + offset_y**2)
            if distance < DEAD_ZONE:
                return

            print(f"调试移动: 目标({target_x}, {target_y}) 偏移({offset_x}, {offset_y}) 距离{distance:.1f}")

            # 角度转换
            angle_per_pixel_h = HORIZONTAL_SENSITIVITY / DETECT_WIDTH
            angle_per_pixel_v = VERTICAL_SENSITIVITY / DETECT_HEIGHT

            angle_offset_h = offset_x * angle_per_pixel_h * HORIZONTAL_DIRECTION
            angle_offset_v = offset_y * angle_per_pixel_v * VERTICAL_DIRECTION

            print(f"角度偏移: 水平{angle_offset_h:.2f}° 纵向{angle_offset_v:.2f}°")

            # 计算目标角度
            self.target_pan = self.current_pan + angle_offset_h
            self.target_tilt = self.current_tilt + angle_offset_v

            # 根据舵机类型限制角度范围
            if IS_270_DEGREE_SERVO:
                self.target_pan = max(10, min(260, self.target_pan))  # 270度舵机范围
            else:
                self.target_pan = max(30, min(150, self.target_pan))  # 180度舵机范围
            self.target_tilt = max(30, min(150, self.target_tilt))    # 垂直轴始终是180度

            # 计算移动步长
            pan_diff = self.target_pan - self.current_pan
            tilt_diff = self.target_tilt - self.current_tilt

            # 限制单次移动步长
            if abs(pan_diff) > MAX_MOVE_STEP:
                pan_step = MAX_MOVE_STEP if pan_diff > 0 else -MAX_MOVE_STEP
            else:
                pan_step = pan_diff

            if abs(tilt_diff) > MAX_MOVE_STEP:
                tilt_step = MAX_MOVE_STEP if tilt_diff > 0 else -MAX_MOVE_STEP
            else:
                tilt_step = tilt_diff

            # 应用平滑因子
            pan_step *= SMOOTHING_FACTOR
            tilt_step *= SMOOTHING_FACTOR

            # 计算新位置
            new_pan = self.current_pan + pan_step
            new_tilt = self.current_tilt + tilt_step

            # 更新历史记录
            self.pan_history.append(new_pan)
            self.tilt_history.append(new_tilt)
            self.pan_history.pop(0)
            self.tilt_history.pop(0)

            # 使用历史平均值进一步平滑
            smooth_pan = sum(self.pan_history) / len(self.pan_history)
            smooth_tilt = sum(self.tilt_history) / len(self.tilt_history)

            print(f"移动到: 水平{smooth_pan:.1f}° 纵向{smooth_tilt:.1f}°")

            # 平滑移动舵机
            self.set_servo_smooth(self.pan_servo, smooth_pan, True)
            self.set_servo_smooth(self.tilt_servo, smooth_tilt, False)

        except Exception as e:
            print("调试移动错误: " + str(e))

    def disable_servos(self):
        """禁用舵机"""
        try:
            if self.pan_servo:
                self.pan_servo.enable(False)
            if self.tilt_servo:
                self.tilt_servo.enable(False)
            print("舵机已禁用")
        except:
            pass

class PreciseBlackRectTracker:
    """精准黑边矩形跟踪器"""

    def __init__(self):
        self.servo = DebugFriendlyServoController()
        self.detector = PreciseBlackRectDetector()
        self.laser = TwoWireLaserController()  # 添加两线激光灯控制器
        self.tracking = False
        self.target_locked = False
        self.tracking_frames = 0
        self.stable_frames = 0

        # 水平搜索相关变量
        self.searching = False
        self.search_pan = SEARCH_PAN_MIN
        self.search_direction = 1  # 1=向右, -1=向左
        self.last_search_time = 0
        self.no_target_frames = 0
        self.search_cycle_count = 0

        print("✅ 精准黑边矩形跟踪器初始化完成")
        print(f"🔍 搜索功能: {'启用' if SEARCH_ENABLED else '禁用'}")
        if SEARCH_ENABLED:
            print(f"🔍 搜索模式: {SEARCH_MODE} (仅水平)")
            print(f"🔍 水平范围: {SEARCH_PAN_MIN}° - {SEARCH_PAN_MAX}°")
            print(f"🔍 垂直固定: {SEARCH_TILT_FIXED}° (水平面)")

    def get_rect_center(self, rect):
        """获取矩形中心"""
        try:
            rect_info = rect.rect()
            x, y, w, h = rect_info
            return x + w // 2, y + h // 2
        except:
            return 0, 0

    def start_search(self):
        """开始水平搜索"""
        if SEARCH_ENABLED and not self.searching:
            self.searching = True
            self.search_pan = SEARCH_PAN_MIN
            self.search_direction = 1
            self.search_cycle_count = 0
            self.last_search_time = time.ticks_ms()

            # 移动到起始位置
            self.servo.set_servo_smooth(self.servo.pan_servo, self.search_pan, True)
            # 垂直轴固定在水平面
            self.servo.set_servo_smooth(self.servo.tilt_servo, SEARCH_TILT_FIXED, False)

            print(f"🔍 开始水平搜索")
            print(f"🔍 水平范围: {SEARCH_PAN_MIN}° - {SEARCH_PAN_MAX}°")
            print(f"🔍 垂直固定: {SEARCH_TILT_FIXED}° (保持水平)")
            return True
        return False

    def update_search(self):
        """更新水平搜索状态"""
        if not self.searching or not SEARCH_ENABLED:
            return False

        current_time = time.ticks_ms()
        time_diff = time.ticks_diff(current_time, self.last_search_time)

        # 检查是否需要移动
        if time_diff >= (SEARCH_DELAY * 1000):
            return self._update_horizontal_search()

        return True

    def _update_horizontal_search(self):
        """水平搜索 (仅左右移动)"""
        # 计算下一个角度
        self.search_pan += self.search_direction * SEARCH_STEP_SIZE

        # 检查边界并改变方向
        if self.search_pan >= SEARCH_PAN_MAX:
            self.search_pan = SEARCH_PAN_MAX
            self.search_direction = -1
            self.search_cycle_count += 1  # 完成一个来回
        elif self.search_pan <= SEARCH_PAN_MIN:
            self.search_pan = SEARCH_PAN_MIN
            self.search_direction = 1
            self.search_cycle_count += 1  # 完成一个来回

        # 移动舵机 (只移动水平轴)
        self.servo.set_servo_smooth(self.servo.pan_servo, self.search_pan, True)
        self.last_search_time = time.ticks_ms()

        # 显示搜索进度
        direction_str = "→" if self.search_direction > 0 else "←"
        print(f"🔍 水平搜索 {direction_str} {self.search_pan}° (周期:{self.search_cycle_count})")
        return True



    def stop_search(self):
        """停止搜索"""
        if self.searching:
            self.searching = False
            print(f"🔍 水平搜索停止，位置: {self.search_pan}°")
            print(f"🔍 搜索统计: 完成{self.search_cycle_count}个来回")
            return True
        return False

    def track_black_rect(self, rectangles):
        """跟踪黑边矩形"""
        try:
            if not rectangles:
                self.no_target_frames += 1

                # 如果正在跟踪，停止跟踪
                if self.tracking:
                    print("🔍 黑边矩形丢失，停止跟踪")
                    self.tracking = False
                    self.target_locked = False
                    self.tracking_frames = 0
                    self.stable_frames = 0
                    # 🔴 丢失目标时关闭激光灯
                    self.laser.turn_off_laser()

                # 如果连续多帧没有目标，开始搜索
                if self.no_target_frames > 30 and SEARCH_ENABLED and not self.searching:
                    print("🔍 未检测到目标，开始水平搜索...")
                    self.start_search()

                # 如果正在搜索，继续搜索
                if self.searching:
                    self.update_search()

                return None

            # 检测到目标，停止搜索
            if self.searching:
                print("🎯 检测到目标，停止搜索")
                self.stop_search()

            self.no_target_frames = 0

            # 获取矩形中心
            target_rect = rectangles[0]
            center_x, center_y = self.get_rect_center(target_rect)

            # 计算距离
            image_center_x = DETECT_WIDTH // 2
            image_center_y = DETECT_HEIGHT // 2
            distance = math.sqrt((center_x - image_center_x)**2 + (center_y - image_center_y)**2)

            # 稳定性检查
            if distance < LOCK_TOLERANCE:
                self.stable_frames += 1
                if self.stable_frames >= STABILITY_FRAMES:
                    if not self.target_locked:
                        print("🎯 激光精准锁定黑边矩形: (" + str(center_x) + ", " + str(center_y) +
                              ") 距离: " + str(round(distance, 1)) + "像素")
                        self.target_locked = True
                        # 🔴 目标锁定后开启激光灯
                        self.laser.turn_on_laser()
                    return center_x, center_y
            else:
                self.stable_frames = 0
                # 🔴 失去锁定时关闭激光灯
                if self.target_locked:
                    self.laser.turn_off_laser()
                self.target_locked = False

            # 开始或继续跟踪
            if not self.tracking:
                print("🔄 开始跟踪黑边矩形: (" + str(center_x) + ", " + str(center_y) + ")")
                self.tracking = True
                self.tracking_frames = 0

            self.tracking_frames += 1

            # 调试友好的移动到目标
            self.servo.debug_move_to_target(center_x, center_y)

            return center_x, center_y

        except Exception as e:
            print("跟踪错误: " + str(e))
            return None

def init_high_performance_system():
    """初始化高性能系统"""
    global sensor

    try:
        print("初始化高性能摄像头系统...")

        sensor = Sensor()
        sensor.reset()

        # 设置主通道 (显示用)
        sensor.set_framesize(width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT)
        sensor.set_pixformat(Sensor.YUV420SP)

        # 绑定到显示层
        bind_info = sensor.bind_info()
        Display.bind_layer(**bind_info, layer=Display.LAYER_VIDEO1)

        # 设置检测通道 (检测用)
        sensor.set_framesize(width=DETECT_WIDTH, height=DETECT_HEIGHT, chn=CAM_CHN_ID_1)
        sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_1)

        # 初始化显示
        Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, to_ide=True)
        MediaManager.init()
        sensor.run()

        print("✅ 高性能系统初始化成功")
        return True

    except Exception as e:
        print("❌ 系统初始化失败: " + str(e))
        return False

def cleanup_system():
    """清理系统资源"""
    print("🧹 清理系统资源...")

    # 基本清理 - 不抛出异常
    try:
        global sensor
        if 'sensor' in globals() and sensor:
            sensor.stop()
    except:
        pass

    try:
        Display.deinit()
    except:
        pass

    try:
        MediaManager.deinit()
    except:
        pass

    try:
        gc.collect()
    except:
        pass

    try:
        os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    except:
        pass

    print("✅ 系统清理完成")

def create_debug_display(detect_img, rectangles, tracker, frame_count, fps_val):
    """创建调试显示"""
    try:
        # 创建叠加层图像
        overlay_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)
        overlay_img.clear(color=(0, 0, 0, 0))

        # 计算缩放比例
        scale_x = DISPLAY_WIDTH / DETECT_WIDTH
        scale_y = DISPLAY_HEIGHT / DETECT_HEIGHT

        # 绘制图像中心标记
        center_x = DISPLAY_WIDTH // 2
        center_y = DISPLAY_HEIGHT // 2

        # 十字线
        overlay_img.draw_line(center_x - 25, center_y, center_x + 25, center_y,
                             color=(255, 255, 0, 255), thickness=3)
        overlay_img.draw_line(center_x, center_y - 25, center_x, center_y + 25,
                             color=(255, 255, 0, 255), thickness=3)

        # 锁定容差圆圈
        overlay_img.draw_circle(center_x, center_y, int(LOCK_TOLERANCE * scale_x),
                               color=(255, 255, 0, 150), thickness=2)

        # 绘制检测到的黑边矩形
        for i, rect in enumerate(rectangles):
            try:
                rect_info = rect.rect()

                # 坐标转换到显示分辨率
                x = int(rect_info[0] * scale_x)
                y = int(rect_info[1] * scale_y)
                w = int(rect_info[2] * scale_x)
                h = int(rect_info[3] * scale_y)

                # 根据状态选择颜色
                if tracker.target_locked:
                    color = (0, 255, 0, 255)      # 绿色 - 已锁定
                elif tracker.tracking:
                    color = (255, 165, 0, 255)    # 橙色 - 跟踪中
                else:
                    color = (255, 0, 0, 255)      # 红色 - 检测到

                # 加粗矩形框
                overlay_img.draw_rectangle(x, y, w, h, color=color, thickness=5)

                # 中心点
                rect_center_x = x + w // 2
                rect_center_y = y + h // 2
                overlay_img.draw_circle(rect_center_x, rect_center_y, 12, color=color, thickness=4)

                # 角点标记
                corners = rect.corners()
                for corner in corners:
                    cx = int(corner[0] * scale_x)
                    cy = int(corner[1] * scale_y)
                    overlay_img.draw_circle(cx, cy, 6, color=(255, 255, 0, 255), thickness=3)

                # 连线
                overlay_img.draw_line(center_x, center_y, rect_center_x, rect_center_y,
                                     color=(0, 255, 255, 200), thickness=3)

                # 详细标签
                area = rect_info[2] * rect_info[3]
                orig_center_x, orig_center_y = tracker.get_rect_center(rect)
                distance = math.sqrt((orig_center_x - DETECT_WIDTH//2)**2 + (orig_center_y - DETECT_HEIGHT//2)**2)

                label = f"黑边矩形 面积:{int(area)} 距离:{round(distance, 1)}px"
                overlay_img.draw_string_advanced(x, y - 30, 16, label, color=color)

            except:
                continue

        # 状态信息
        if tracker.searching:
            direction_str = "→" if tracker.search_direction > 0 else "←"
            status = f"🔍 水平搜索 {direction_str} {tracker.search_pan}° (周期:{tracker.search_cycle_count})"
            status_color = (255, 255, 0, 255)
        elif tracker.target_locked:
            status = "🎯 激光精准锁定黑边矩形"
            status_color = (0, 255, 0, 255)
        elif tracker.tracking:
            status = f"🔄 跟踪黑边矩形 (帧:{tracker.tracking_frames})"
            status_color = (255, 165, 0, 255)
        else:
            status = "🔍 等待黑边矩形"
            status_color = (255, 0, 0, 255)

        overlay_img.draw_string_advanced(20, 20, 20, status, color=status_color)

        # 检测信息
        confidence = tracker.detector.confidence
        detect_info = f"检测置信度: {confidence} | 稳定帧: {tracker.stable_frames}"
        overlay_img.draw_string_advanced(20, 50, 14, detect_info, color=(255, 255, 255, 255))

        # 性能信息
        perf_text = f"FPS: {fps_val:.1f} | 帧: {frame_count} | 黑边矩形: {len(rectangles)}"
        overlay_img.draw_string_advanced(20, 70, 14, perf_text, color=(255, 255, 255, 255))

        # 舵机调试信息
        angle_text = f"舵机: H{round(tracker.servo.current_pan, 1)}° V{round(tracker.servo.current_tilt, 1)}°"
        overlay_img.draw_string_advanced(20, 90, 14, angle_text, color=(255, 255, 255, 255))

        # 激光灯状态信息
        laser_status = "🔴 激光开启" if tracker.laser.is_laser_on() else "⚫ 激光关闭"
        laser_color = (255, 0, 0, 255) if tracker.laser.is_laser_on() else (128, 128, 128, 255)
        overlay_img.draw_string_advanced(20, 110, 14, laser_status, color=laser_color)

        # GPIO状态信息
        positive, negative = tracker.laser.get_gpio_status()
        gpio_status = f"GPIO: +{positive} -{negative}"
        overlay_img.draw_string_advanced(20, 130, 14, gpio_status, color=(200, 200, 200, 255))

        # 移动参数信息
        param_text = f"移动: 步长{MAX_MOVE_STEP}° 延迟{MOVE_DELAY_MS}ms 平滑{SMOOTHING_FACTOR}"
        overlay_img.draw_string_advanced(20, 150, 12, param_text, color=(255, 255, 255, 255))

        return overlay_img

    except Exception as e:
        print("显示创建错误: " + str(e))
        blank_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)
        blank_img.clear(color=(0, 0, 0, 0))
        return blank_img

def main():
    """主程序"""
    global sensor
    sensor = None

    os.exitpoint(os.EXITPOINT_ENABLE)
    system_ready = False

    try:
        print("=== K230 精准黑边矩形激光跟踪系统 (两线激光灯版) ===")
        print("专门识别黑边矩形框，增加激光笔继电器控制功能")
        print("黑边检测参数:")
        print(f"- 黑边阈值: {BLACK_EDGE_THRESHOLD} (0-255)")
        print(f"- 边缘强度: {MIN_EDGE_STRENGTH}")
        print(f"- 矩形度要求: {MIN_RECTANGULARITY}")
        print(f"- 角点容差: {CORNER_TOLERANCE}°")
        print("🔧 用户初始位置设置:")
        print(f"- 水平初始角度: {INITIAL_PAN_ANGLE}° (可在代码顶部修改)")
        print(f"- 垂直初始角度: {INITIAL_TILT_ANGLE}° (可在代码顶部修改)")
        print(f"- 水平舵机类型: {'270度舵机' if IS_270_DEGREE_SERVO else '180度舵机'}")
        print("270度水平搜索参数:")
        print(f"- 搜索功能: {'启用' if SEARCH_ENABLED else '禁用'}")
        print(f"- 搜索模式: {SEARCH_MODE} (仅水平)")
        print(f"- 水平范围: {SEARCH_PAN_MIN}° - {SEARCH_PAN_MAX}° (270度覆盖)")
        print(f"- 垂直固定: {SEARCH_TILT_FIXED}° (用户设定位置)")
        print(f"- 搜索速度: {SEARCH_SPEED}°/秒")
        print(f"- 步长: {SEARCH_STEP_SIZE}°")
        print("调试移动参数:")
        print(f"- 移动步长: {MAX_MOVE_STEP}°")
        print(f"- 移动延迟: {MOVE_DELAY_MS}ms")
        print(f"- 平滑因子: {SMOOTHING_FACTOR}")
        print(f"- 灵敏度: H{HORIZONTAL_SENSITIVITY} V{VERTICAL_SENSITIVITY}")
        print("两线激光灯参数:")
        print(f"- 正极控制GPIO: {LASER_POSITIVE_GPIO} (庐山派引脚8)")
        print(f"- 负极控制GPIO: {LASER_NEGATIVE_GPIO} (庐山派引脚10)")
        print(f"- 控制逻辑: 锁定目标后自动开启激光灯")
        print()

        if not init_high_performance_system():
            return

        system_ready = True
        tracker = PreciseBlackRectTracker()

        if tracker.servo.pan_servo is None or tracker.servo.tilt_servo is None:
            print("❌ 舵机初始化失败")
            return

        print("🚀 系统就绪，开始精准黑边矩形跟踪...")
        print("📍 请将黑边矩形物体放在摄像头前")
        if SEARCH_ENABLED:
            print(f"🔍 未检测到目标时将自动开始270度水平搜索")
            print(f"🔍 水平范围: {SEARCH_PAN_MIN}° - {SEARCH_PAN_MAX}° (270度覆盖)")
            print(f"🔍 垂直固定: {SEARCH_TILT_FIXED}° (用户设定位置)")
            print(f"💡 提示: 可在代码顶部修改 INITIAL_PAN_ANGLE 和 INITIAL_TILT_ANGLE 调整初始位置")

        fps = time.clock()
        frame_count = 0

        while True:
            try:
                fps.tick()
                frame_count += 1

                # K230专用的退出点检查 - 必须在每次循环中调用
                os.exitpoint()

                # 获取检测图像
                detect_img = None
                try:
                    detect_img = sensor.snapshot(chn=CAM_CHN_ID_1)
                except Exception as e:
                    if frame_count % 60 == 0:
                        print("摄像头错误: " + str(e))
                    continue

                # 精准检测黑边矩形
                rectangles = []
                if detect_img is not None:
                    rectangles = tracker.detector.detect_black_rectangles(detect_img)

                # 跟踪黑边矩形
                target_center = tracker.track_black_rect(rectangles)

                # 创建调试显示
                overlay_img = create_debug_display(detect_img, rectangles, tracker, frame_count, fps.fps())

                try:
                    # 显示叠加层
                    Display.show_image(overlay_img, layer=Display.LAYER_OSD0, alpha=220)
                except Exception as e:
                    if frame_count % 60 == 0:
                        print("显示错误: " + str(e))

                # 垃圾回收
                if frame_count % 120 == 0:
                    gc.collect()

                # 状态报告
                if frame_count % 180 == 0:
                    confidence = tracker.detector.confidence

                    if tracker.searching:
                        status = f"🔍 搜索{tracker.search_pan}°"
                    elif tracker.target_locked:
                        status = "🎯 锁定"
                    elif tracker.tracking:
                        status = "🔄 跟踪"
                    else:
                        status = "🔍 等待"

                    print("FPS: " + str(round(fps.fps(), 1)) +
                          ", 黑边矩形: " + str(len(rectangles)) +
                          ", 状态: " + status +
                          ", 置信度: " + str(confidence))

            except Exception as e:
                error_msg = str(e)
                # 检查是否是IDE中断
                if "IDE interrupt" in error_msg or "interrupt" in error_msg.lower():
                    print("\n⏹️ 检测到IDE停止信号，正在安全退出...")
                    break
                else:
                    print("主循环错误: " + error_msg)
                    time.sleep_ms(50)
                    continue

    except Exception as e:
        error_msg = str(e)
        if "IDE interrupt" in error_msg or "interrupt" in error_msg.lower():
            print("\n⏹️ IDE停止程序")
        else:
            print("\n❌ 程序异常: " + error_msg)
    finally:
        print("\n🧹 开始安全退出程序...")

        # 停止舵机和激光笔
        try:
            if 'tracker' in locals() and tracker:
                print("🔹 停止舵机...")
                tracker.servo.disable_servos()
                print("🔹 关闭激光灯...")
                tracker.laser.cleanup()
                time.sleep_ms(100)
                print("✅ 舵机和激光灯已停止")
        except Exception as e:
            print(f"舵机/激光灯停止错误: {e}")

        # 清理系统资源
        if system_ready:
            try:
                cleanup_system()
            except Exception as e:
                print(f"系统清理错误: {e}")

        # 最终清理
        try:
            import gc
            gc.collect()
            time.sleep_ms(200)
        except:
            pass

        print("✅ 程序安全退出完成")
        print("💡 提示: 如果仍有问题，请重启K230开发板")

if __name__ == "__main__":
    # 使用安全包装器运行主程序
    if INTERRUPT_WRAPPER_AVAILABLE:
        print("🔧 使用K230安全中断处理模式")
        safe_run_with_interrupt_handling(main)
    else:
        print("⚠️ 使用标准模式 (可能出现IDE中断问题)")
        main()
