# A4纸张黑边框识别与二维云台定位
# 适用于K230平台，CANM编译环境
# 功能：识别A4纸张的2cm宽黑边框，控制二维云台将激光点定位到矩形框中心

import time
import os
import sys
import math

from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import FPIOA
from machine import Pin
from machine import PWM

sensor = None

class PID:
    """PID控制器类"""
    def __init__(self, kp, ki, kd, target=320):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.target = target
        self.e = 0
        self.e_last = 0
        self.e_sum = 0
        
    def cal(self, value):
        """计算PID输出"""
        self.e = self.target - value
        self.e_sum += self.e
        delta = self.kp * self.e + self.ki * self.e_sum + self.kd * (self.e - self.e_last)
        self.e_last = self.e
        return delta

class A4Detector:
    """A4纸张检测器类"""
    def __init__(self):
        # A4纸张尺寸比例 (宽:高 = 1:1.414)
        self.a4_ratio = 1.414
        self.min_area = 50000  # 最小面积阈值
        self.max_area = 200000  # 最大面积阈值
        
    def detect_a4_paper(self, img):
        """检测A4纸张"""
        # 转换为灰度图
        img_gray = img.to_grayscale(copy=True)
        
        # 二值化处理，识别黑色边框
        # 根据实际光照条件调整阈值
        img_binary = img_gray.binary([(0, 100)])
        
        # 查找矩形
        rects = img_binary.find_rects(threshold=10000)
        
        if rects is None:
            return None, None
            
        best_rect = None
        best_score = 0
        
        for rect in rects:
            # 计算矩形面积
            area = rect.w() * rect.h()
            
            # 检查面积是否在合理范围内
            if area < self.min_area or area > self.max_area:
                continue
                
            # 计算长宽比
            aspect_ratio = rect.w() / rect.h()
            
            # 检查长宽比是否接近A4纸张比例
            ratio_diff = abs(aspect_ratio - self.a4_ratio)
            if ratio_diff > 0.3:  # 允许一定的误差
                continue
                
            # 计算综合评分（面积越大，比例越接近，评分越高）
            score = area * (1 - ratio_diff)
            
            if score > best_score:
                best_score = score
                best_rect = rect
                
        return best_rect, best_score
    
    def get_center_point(self, rect):
        """获取矩形中心点"""
        if rect is None:
            return None
            
        center_x = rect.x() + rect.w() // 2
        center_y = rect.y() + rect.h() // 2
        return (center_x, center_y)

class ServoController:
    """舵机控制器类"""
    def __init__(self):
        # 初始化FPIOA
        self.fpioa = FPIOA()
        
        # 设置舵机引脚
        self.fpioa.set_function(46, FPIOA.PWM2)  # 水平舵机
        self.fpioa.set_function(42, FPIOA.PWM0)  # 垂直舵机
        
        # 初始化PWM
        self.pwm_h = PWM(2, 50)  # 水平舵机
        self.pwm_v = PWM(0, 50)  # 垂直舵机
        
        # 舵机角度范围限制
        self.h_min_duty = 0.5 / 20 * 100  # 最小占空比
        self.h_max_duty = 2.5 / 20 * 100  # 最大占空比
        self.v_min_duty = 0.5 / 20 * 100
        self.v_max_duty = 2.5 / 20 * 100
        
        # 当前舵机位置
        self.h_current_duty = 1.5 / 20 * 100
        self.v_current_duty = 1.5 / 20 * 100
        
        # 初始化舵机位置
        self.set_servo_position(self.h_current_duty, self.v_current_duty)
        
    def set_servo_position(self, h_duty, v_duty):
        """设置舵机位置"""
        # 限制占空比范围
        h_duty = max(self.h_min_duty, min(self.h_max_duty, h_duty))
        v_duty = max(self.v_min_duty, min(self.v_max_duty, v_duty))
        
        # 设置舵机位置
        self.pwm_h.duty(h_duty)
        self.pwm_v.duty(v_duty)
        
        self.h_current_duty = h_duty
        self.v_current_duty = v_duty
        
    def move_to_target(self, target_x, target_y, img_width=640, img_height=640):
        """移动到目标位置"""
        # 计算目标位置相对于图像中心的偏移
        center_x = img_width // 2
        center_y = img_height // 2
        
        offset_x = target_x - center_x
        offset_y = target_y - center_y
        
        # PID控制参数
        kp = 0.001
        ki = 0.0001
        kd = 0.0005
        
        # 计算新的舵机位置
        h_delta = offset_x * kp
        v_delta = offset_y * kp
        
        new_h_duty = self.h_current_duty + h_delta
        new_v_duty = self.v_current_duty + v_delta
        
        # 设置新位置
        self.set_servo_position(new_h_duty, new_v_duty)
        
        return abs(offset_x) < 10 and abs(offset_y) < 10  # 返回是否到达目标

try:
    print("A4纸张识别与云台定位系统启动")
    
    # 初始化传感器
    sensor = Sensor(width=640, height=640)
    sensor.reset()
    sensor.set_framesize(width=640, height=640)
    sensor.set_pixformat(Sensor.RGB565)
    
    # 初始化显示
    Display.init(Display.LT9611, to_ide=True)
    MediaManager.init()
    sensor.run()
    
    # 初始化A4检测器和舵机控制器
    detector = A4Detector()
    servo_controller = ServoController()
    
    # 初始化激光笔
    fpioa = FPIOA()
    fpioa.set_function(33, FPIOA.GPIO33)
    laser_pin = Pin(33, Pin.OUT)
    laser_pin.value(0)  # 初始关闭激光笔
    
    clock = time.clock()
    
    # 状态变量
    detection_mode = True  # True: 检测模式, False: 跟踪模式
    target_center = None
    lock_count = 0  # 锁定计数器
    
    print("系统初始化完成，开始运行...")
    
    while True:
        clock.tick()
        os.exitpoint()
        
        # 获取图像
        img = sensor.snapshot(chn=CAM_CHN_ID_0)
        
        if detection_mode:
            # 检测模式：寻找A4纸张
            rect, score = detector.detect_a4_paper(img)
            
            if rect is not None:
                # 绘制检测到的矩形
                corner = rect.corners()
                for i in range(4):
                    start_point = corner[i]
                    end_point = corner[(i + 1) % 4]
                    img.draw_line(start_point[0], start_point[1], 
                                end_point[0], end_point[1], 
                                color=(0, 255, 0), thickness=3)
                
                # 获取矩形中心
                center = detector.get_center_point(rect)
                if center:
                    # 绘制中心点
                    img.draw_circle(center[0], center[1], 10, 
                                  color=(255, 0, 0), thickness=2, fill=True)
                    
                    # 显示检测信息
                    img.draw_string_advanced(10, 10, 40, 
                                          f"A4检测成功 评分:{score:.0f}", 
                                          color=(0, 255, 0))
                    img.draw_string_advanced(10, 50, 30, 
                                          f"中心:({center[0]},{center[1]})", 
                                          color=(255, 255, 0))
                    
                    # 如果检测到合适的A4纸张，切换到跟踪模式
                    if score > 80000:  # 根据实际情况调整阈值
                        target_center = center
                        detection_mode = False
                        laser_pin.value(1)  # 开启激光笔
                        print(f"检测到A4纸张，中心位置: {center}")
                        
            else:
                img.draw_string_advanced(10, 10, 40, "未检测到A4纸张", 
                                      color=(255, 0, 0))
                
        else:
            # 跟踪模式：控制云台定位到目标中心
            if target_center:
                # 重新检测A4纸张以获取当前中心位置
                rect, score = detector.detect_a4_paper(img)
                
                if rect is not None:
                    current_center = detector.get_center_point(rect)
                    
                    if current_center:
                        # 绘制目标中心点
                        img.draw_circle(target_center[0], target_center[1], 15, 
                                      color=(0, 0, 255), thickness=3, fill=False)
                        
                        # 绘制当前检测到的中心点
                        img.draw_circle(current_center[0], current_center[1], 10, 
                                      color=(255, 0, 0), thickness=2, fill=True)
                        
                        # 控制云台移动到目标位置
                        is_locked = servo_controller.move_to_target(
                            target_center[0], target_center[1])
                        
                        # 显示跟踪信息
                        img.draw_string_advanced(10, 10, 40, "跟踪模式", 
                                              color=(0, 255, 0))
                        img.draw_string_advanced(10, 50, 30, 
                                              f"目标:({target_center[0]},{target_center[1]})", 
                                              color=(0, 0, 255))
                        img.draw_string_advanced(10, 80, 30, 
                                              f"当前:({current_center[0]},{current_center[1]})", 
                                              color=(255, 0, 0))
                        
                        if is_locked:
                            lock_count += 1
                            img.draw_string_advanced(10, 110, 40, 
                                                  f"已锁定! 计数:{lock_count}", 
                                                  color=(0, 255, 255))
                        else:
                            lock_count = 0
                            
                        # 如果锁定时间足够长，可以执行其他操作
                        if lock_count > 30:  # 约1秒（假设30fps）
                            img.draw_string_advanced(10, 150, 40, 
                                                  "定位完成！", 
                                                  color=(255, 255, 0))
                else:
                    # 如果丢失目标，返回检测模式
                    detection_mode = True
                    laser_pin.value(0)  # 关闭激光笔
                    lock_count = 0
                    print("丢失目标，返回检测模式")
        
        # 显示FPS
        img.draw_string_advanced(10, img.height() - 40, 30, 
                              f"FPS: {clock.fps():.1f}", 
                              color=(255, 255, 255))
        
        # 显示模式信息
        mode_text = "检测模式" if detection_mode else "跟踪模式"
        img.draw_string_advanced(img.width() - 150, 10, 30, mode_text, 
                              color=(255, 255, 0))
        
        # 显示图像
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止程序: ", e)
except BaseException as e:
    print(f"程序异常: {e}")
finally:
    # 清理资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
    print("程序已退出") 