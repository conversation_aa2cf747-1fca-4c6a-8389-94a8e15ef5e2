"""
简单测试脚本 - 验证阈值调节界面
"""

# 模拟一些基本的类和函数来测试界面逻辑
class MockImage:
    def __init__(self, width, height, format):
        self.width = width
        self.height = height
        self.format = format
    
    def draw_rectangle(self, x, y, w, h, color, thickness=1, fill=False):
        print(f"绘制矩形: ({x}, {y}, {w}, {h}) 颜色:{color}")
    
    def draw_string_advanced(self, x, y, size, text, color):
        print(f"绘制文字: '{text}' 位置:({x}, {y}) 大小:{size}")
    
    def draw_image(self, img, x, y):
        print(f"绘制图像: 位置({x}, {y})")

# 模拟image模块
class image:
    RGB565 = "RGB565"
    
    @staticmethod
    def Image(width, height, format):
        return MockImage(width, height, format)

# 模拟TOUCH类
class TOUCH:
    def __init__(self, id):
        self.id = id
        self.touch_count = 0
    
    def read(self):
        # 模拟触摸点
        self.touch_count += 1
        if self.touch_count % 50 == 0:  # 每50次调用返回一个触摸点
            return [type('Point', (), {'x': 100, 'y': 150})]
        return []

# 测试阈值调节器
BUTTON_COLOR = (150, 150, 150)
TEXT_COLOR = (0, 0, 0)
THRESHOLD_STEP = 2
TOUCH_COUNTER_THRESHOLD = 20

class OfflineThresholdAdjuster:
    def __init__(self):
        self.touch_counter = 0
        self.tp = TOUCH(0)
        print("✅ 模拟阈值调节器初始化成功")
    
    def check_long_press(self):
        """检查长按屏幕进入阈值编辑模式"""
        try:
            points = self.tp.read()
            if len(points) > 0:
                self.touch_counter += 1
                print(f"触摸计数: {self.touch_counter}/{TOUCH_COUNTER_THRESHOLD}")
                if self.touch_counter > TOUCH_COUNTER_THRESHOLD:
                    return True
            else:
                self.touch_counter -= 2
                self.touch_counter = max(0, self.touch_counter)
            return False
        except:
            return False
    
    def which_button(self, x, y):
        """判断按下的按钮是哪一个"""
        print(f"检测按钮点击: ({x}, {y})")
        
        # 顶部按钮行
        if y < 60:
            if x < 160:
                return "return"
            elif x > 640:
                return "change"
        
        # 底部按钮行
        elif y > 420:
            if x < 160:
                return "reset"
            elif x > 640:
                return "save"
        
        # 中间区域的加减按钮
        elif 120 < y < 360:
            # 黑边阈值控制区域
            if 50 < x < 350:
                if 120 < y < 180:  # 黑边阈值减少
                    return "black_edge_minus"
                elif 200 < y < 260:  # 黑边阈值增加
                    return "black_edge_plus"
            
            # 矩形检测阈值控制区域
            elif 450 < x < 750:
                if 120 < y < 180:  # 矩形阈值减少
                    return "rect_minus"
                elif 200 < y < 260:  # 矩形阈值增加
                    return "rect_plus"
        
        return None
    
    def create_threshold_ui(self):
        """创建简化的阈值调整界面"""
        print("创建阈值调整界面...")
        
        # 创建一个画布，用来绘制按钮
        img = image.Image(800, 480, image.RGB565)
        img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)
        
        # 顶部按钮
        img.draw_rectangle(0, 0, 160, 60, color=BUTTON_COLOR, thickness=2, fill=True)
        img.draw_string_advanced(50, 20, 30, "返回", color=TEXT_COLOR)
        
        img.draw_rectangle(640, 0, 160, 60, color=BUTTON_COLOR, thickness=2, fill=True)
        img.draw_string_advanced(690, 20, 30, "切换", color=TEXT_COLOR)
        
        # 底部按钮
        img.draw_rectangle(0, 420, 160, 60, color=BUTTON_COLOR, thickness=2, fill=True)
        img.draw_string_advanced(50, 440, 30, "归位", color=TEXT_COLOR)
        
        img.draw_rectangle(640, 420, 160, 60, color=BUTTON_COLOR, thickness=2, fill=True)
        img.draw_string_advanced(690, 440, 30, "保存", color=TEXT_COLOR)
        
        # 左侧：黑边阈值控制
        img.draw_string_advanced(120, 80, 24, "黑边阈值", color=TEXT_COLOR)
        img.draw_rectangle(50, 120, 300, 60, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(170, 140, 30, "减少", color=(255, 255, 255))
        
        img.draw_rectangle(50, 200, 300, 60, color=(100, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(170, 220, 30, "增加", color=(255, 255, 255))
        
        # 右侧：矩形检测阈值控制
        img.draw_string_advanced(520, 80, 24, "矩形阈值", color=TEXT_COLOR)
        img.draw_rectangle(450, 120, 300, 60, color=(255, 100, 100), thickness=2, fill=True)
        img.draw_string_advanced(570, 140, 30, "减少", color=(255, 255, 255))
        
        img.draw_rectangle(450, 200, 300, 60, color=(100, 255, 100), thickness=2, fill=True)
        img.draw_string_advanced(570, 220, 30, "增加", color=(255, 255, 255))
        
        return img

def test_threshold_adjuster():
    """测试阈值调节器"""
    print("=== 测试阈值调节器 ===")
    
    adjuster = OfflineThresholdAdjuster()
    
    # 测试界面创建
    ui = adjuster.create_threshold_ui()
    print("✅ 界面创建成功")
    
    # 测试按钮检测
    test_points = [
        (100, 30),   # 返回按钮
        (700, 30),   # 切换按钮
        (100, 450),  # 重置按钮
        (700, 450),  # 保存按钮
        (200, 150),  # 黑边减少
        (200, 230),  # 黑边增加
        (600, 150),  # 矩形减少
        (600, 230),  # 矩形增加
        (400, 300),  # 无效区域
    ]
    
    for x, y in test_points:
        button = adjuster.which_button(x, y)
        print(f"点击 ({x}, {y}) -> 按钮: {button}")
    
    # 测试长按检测
    print("\n测试长按检测:")
    for i in range(25):
        if adjuster.check_long_press():
            print(f"✅ 第{i+1}次检测到长按")
            break
    
    print("✅ 测试完成")

if __name__ == "__main__":
    test_threshold_adjuster()
